"""
Secrets management for secure handling of sensitive configuration.
"""
import os
import json
import base64
from typing import Optional, Dict, Any
from pathlib import Path
from loguru import logger

from core.config import get_settings

settings = get_settings()


class SecretsManager:
    """
    Simple secrets manager for handling sensitive configuration.
    
    Supports multiple backends:
    - Environment variables (default)
    - JSON file (for development)
    - Base64 encoded environment variables
    """
    
    def __init__(self):
        self.secrets_cache: Dict[str, str] = {}
        self.secrets_file_path = Path("secrets.json")
        
    def get_secret(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """
        Get a secret value by key.
        
        Priority order:
        1. Environment variable
        2. Base64 encoded environment variable (key + "_B64")
        3. Secrets file
        4. Cache
        5. Default value
        
        Args:
            key: Secret key name
            default: Default value if secret not found
            
        Returns:
            Secret value or default
        """
        # Check environment variable first
        value = os.getenv(key)
        if value:
            return value
        
        # Check base64 encoded environment variable
        b64_key = f"{key}_B64"
        b64_value = os.getenv(b64_key)
        if b64_value:
            try:
                decoded = base64.b64decode(b64_value).decode('utf-8')
                return decoded
            except Exception as e:
                logger.warning(f"Failed to decode base64 secret {b64_key}: {e}")
        
        # Check secrets file (development only)
        if self.secrets_file_path.exists() and settings.DEBUG:
            try:
                file_value = self._get_from_file(key)
                if file_value:
                    return file_value
            except Exception as e:
                logger.warning(f"Failed to read secret from file: {e}")
        
        # Check cache
        if key in self.secrets_cache:
            return self.secrets_cache[key]
        
        # Return default
        return default
    
    def set_secret(self, key: str, value: str, persist: bool = False):
        """
        Set a secret value.
        
        Args:
            key: Secret key name
            value: Secret value
            persist: Whether to persist to secrets file (development only)
        """
        # Store in cache
        self.secrets_cache[key] = value
        
        # Persist to file if requested and in debug mode
        if persist and settings.DEBUG:
            self._save_to_file(key, value)
    
    def _get_from_file(self, key: str) -> Optional[str]:
        """Get secret from JSON file."""
        if not self.secrets_file_path.exists():
            return None
        
        try:
            with open(self.secrets_file_path, 'r') as f:
                secrets = json.load(f)
                return secrets.get(key)
        except Exception as e:
            logger.error(f"Error reading secrets file: {e}")
            return None
    
    def _save_to_file(self, key: str, value: str):
        """Save secret to JSON file."""
        try:
            # Read existing secrets
            secrets = {}
            if self.secrets_file_path.exists():
                with open(self.secrets_file_path, 'r') as f:
                    secrets = json.load(f)
            
            # Update with new secret
            secrets[key] = value
            
            # Write back to file
            with open(self.secrets_file_path, 'w') as f:
                json.dump(secrets, f, indent=2)
            
            # Set restrictive permissions
            os.chmod(self.secrets_file_path, 0o600)
            
        except Exception as e:
            logger.error(f"Error saving secret to file: {e}")
    
    def get_database_url(self) -> str:
        """Get database URL with secrets."""
        db_type = settings.DATABASE_TYPE.lower()
        
        if db_type == "postgresql":
            # Get database credentials from secrets
            host = self.get_secret("POSTGRES_HOST", "localhost")
            port = self.get_secret("POSTGRES_PORT", "5432")
            user = self.get_secret("POSTGRES_USER", "postgres")
            password = self.get_secret("POSTGRES_PASSWORD")
            database = self.get_secret("POSTGRES_DB", "yt_subs")
            
            if not password:
                raise ValueError("POSTGRES_PASSWORD secret is required for PostgreSQL")
            
            return f"postgresql://{user}:{password}@{host}:{port}/{database}"
        
        else:
            # SQLite
            return "sqlite:///data.db"
    
    def get_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """Get API keys configuration from secrets."""
        # Try to get from secrets first
        api_keys_json = self.get_secret("API_KEYS")
        
        if api_keys_json:
            try:
                return json.loads(api_keys_json)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in API_KEYS secret: {e}")
        
        # Fallback to settings
        if settings.API_KEYS:
            try:
                return json.loads(settings.API_KEYS)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in API_KEYS setting: {e}")
        
        # Return empty dict if no API keys configured
        return {}
    
    def get_gemini_api_key(self) -> Optional[str]:
        """Get Gemini AI API key from secrets."""
        return self.get_secret("GEM_API") or self.get_secret("GEMINI_API_KEY")
    
    def get_youtube_api_key(self) -> Optional[str]:
        """Get YouTube API key from secrets."""
        return self.get_secret("YOUTUBE_API_KEY")
    
    def validate_secrets(self) -> Dict[str, bool]:
        """
        Validate that required secrets are available.
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {}
        
        # Check database secrets
        if settings.DATABASE_TYPE.lower() == "postgresql":
            validation_results["postgres_password"] = bool(self.get_secret("POSTGRES_PASSWORD"))
        
        # Check API keys
        validation_results["api_keys_configured"] = bool(self.get_api_keys())
        
        # Check Gemini API key
        validation_results["gemini_api_key"] = bool(self.get_gemini_api_key())
        
        return validation_results
    
    def mask_secret(self, value: str, show_chars: int = 4) -> str:
        """
        Mask a secret value for logging.
        
        Args:
            value: Secret value to mask
            show_chars: Number of characters to show at the end
            
        Returns:
            Masked secret value
        """
        if not value or len(value) <= show_chars:
            return "*" * len(value) if value else ""
        
        return "*" * (len(value) - show_chars) + value[-show_chars:]
    
    def log_secrets_status(self):
        """Log the status of secrets (without revealing values)."""
        validation = self.validate_secrets()
        
        logger.info("🔐 Secrets validation status:")
        for secret_name, is_valid in validation.items():
            status = "✅" if is_valid else "❌"
            logger.info(f"  {status} {secret_name}: {'configured' if is_valid else 'missing'}")
        
        # Log additional info
        if settings.DEBUG:
            logger.warning("🚨 DEBUG mode enabled - secrets file support active")
        
        if self.secrets_file_path.exists():
            logger.info(f"📁 Secrets file found: {self.secrets_file_path}")
    
    def cleanup_secrets_file(self):
        """Remove secrets file (for production deployment)."""
        if self.secrets_file_path.exists():
            try:
                self.secrets_file_path.unlink()
                logger.info("🗑️ Secrets file removed")
            except Exception as e:
                logger.error(f"Failed to remove secrets file: {e}")


# Global secrets manager instance
secrets_manager = SecretsManager()


def get_secret(key: str, default: Optional[str] = None) -> Optional[str]:
    """Convenience function to get a secret."""
    return secrets_manager.get_secret(key, default)


def validate_production_secrets() -> bool:
    """
    Validate that all required secrets are configured for production.
    
    Returns:
        True if all required secrets are available
    """
    validation = secrets_manager.validate_secrets()
    
    required_secrets = ["api_keys_configured"]
    
    # Add database password requirement for PostgreSQL
    if settings.DATABASE_TYPE.lower() == "postgresql":
        required_secrets.append("postgres_password")
    
    missing_secrets = [
        secret for secret in required_secrets 
        if not validation.get(secret, False)
    ]
    
    if missing_secrets:
        logger.error(f"❌ Missing required secrets for production: {missing_secrets}")
        return False
    
    logger.info("✅ All required secrets are configured for production")
    return True


# Export main components
__all__ = [
    'SecretsManager',
    'secrets_manager',
    'get_secret',
    'validate_production_secrets',
]
