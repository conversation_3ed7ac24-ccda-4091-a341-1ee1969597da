"""
Simple alerting system for monitoring critical application metrics.
"""
import asyncio
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

from core.config import get_settings

settings = get_settings()


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class Alert:
    """Alert data structure."""
    name: str
    severity: AlertSeverity
    message: str
    timestamp: float = field(default_factory=time.time)
    status: AlertStatus = AlertStatus.ACTIVE
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary."""
        return {
            "name": self.name,
            "severity": self.severity.value,
            "message": self.message,
            "timestamp": self.timestamp,
            "status": self.status.value,
            "metadata": self.metadata,
        }


@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    condition: Callable[[Dict[str, Any]], bool]
    severity: AlertSeverity
    message_template: str
    cooldown_seconds: int = 300  # 5 minutes default cooldown
    enabled: bool = True


class AlertManager:
    """Simple alert manager for monitoring application health."""
    
    def __init__(self):
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.last_alert_time: Dict[str, float] = {}
        self.rules: List[AlertRule] = []
        self._setup_default_rules()
    
    def _setup_default_rules(self):
        """Setup default alerting rules."""
        
        # High CPU usage alert
        self.rules.append(AlertRule(
            name="high_cpu_usage",
            condition=lambda metrics: metrics.get("system", {}).get("cpu_percent", 0) > 90,
            severity=AlertSeverity.WARNING,
            message_template="High CPU usage detected: {cpu_percent:.1f}%",
            cooldown_seconds=300
        ))
        
        # High memory usage alert
        self.rules.append(AlertRule(
            name="high_memory_usage",
            condition=lambda metrics: metrics.get("system", {}).get("memory", {}).get("percent", 0) > 90,
            severity=AlertSeverity.WARNING,
            message_template="High memory usage detected: {memory_percent:.1f}%",
            cooldown_seconds=300
        ))
        
        # Low disk space alert
        self.rules.append(AlertRule(
            name="low_disk_space",
            condition=lambda metrics: metrics.get("system", {}).get("disk", {}).get("percent", 0) > 90,
            severity=AlertSeverity.CRITICAL,
            message_template="Low disk space: {disk_percent:.1f}% used",
            cooldown_seconds=600
        ))
        
        # Queue overload alert
        self.rules.append(AlertRule(
            name="queue_overload",
            condition=lambda metrics: any(
                size > 90 for size in metrics.get("queues", {}).get("queue_sizes", {}).values()
            ),
            severity=AlertSeverity.CRITICAL,
            message_template="Task queue overloaded: {max_queue_size} tasks pending",
            cooldown_seconds=180
        ))
        
        # Database connection issues
        self.rules.append(AlertRule(
            name="database_unhealthy",
            condition=lambda metrics: metrics.get("database", {}).get("health", {}).get("status") != "healthy",
            severity=AlertSeverity.CRITICAL,
            message_template="Database health check failed: {db_error}",
            cooldown_seconds=120
        ))
        
        # High error rate alert
        self.rules.append(AlertRule(
            name="high_error_rate",
            condition=lambda metrics: self._check_error_rate(metrics),
            severity=AlertSeverity.WARNING,
            message_template="High error rate detected: {error_rate:.1f}% in last 5 minutes",
            cooldown_seconds=300
        ))
    
    def _check_error_rate(self, metrics: Dict[str, Any]) -> bool:
        """Check if error rate is too high."""
        try:
            app_metrics = metrics.get("application", {})
            status_codes = app_metrics.get("status_codes", {})
            
            total_requests = sum(status_codes.values())
            if total_requests < 10:  # Not enough data
                return False
            
            error_requests = sum(
                count for status, count in status_codes.items()
                if int(status) >= 400
            )
            
            error_rate = (error_requests / total_requests) * 100
            return error_rate > 10  # Alert if error rate > 10%
            
        except Exception:
            return False
    
    def check_metrics(self, metrics: Dict[str, Any]) -> List[Alert]:
        """Check metrics against alert rules and generate alerts."""
        new_alerts = []
        current_time = time.time()
        
        for rule in self.rules:
            if not rule.enabled:
                continue
            
            try:
                # Check if condition is met
                if rule.condition(metrics):
                    # Check cooldown
                    last_alert = self.last_alert_time.get(rule.name, 0)
                    if current_time - last_alert < rule.cooldown_seconds:
                        continue
                    
                    # Create alert
                    alert = self._create_alert(rule, metrics)
                    new_alerts.append(alert)
                    
                    # Update tracking
                    self.active_alerts[rule.name] = alert
                    self.last_alert_time[rule.name] = current_time
                    self.alert_history.append(alert)
                    
                    # Log alert
                    self._log_alert(alert)
                
                else:
                    # Condition not met, resolve alert if active
                    if rule.name in self.active_alerts:
                        self._resolve_alert(rule.name)
            
            except Exception as e:
                logger.error(f"Error checking alert rule {rule.name}: {e}")
        
        return new_alerts
    
    def _create_alert(self, rule: AlertRule, metrics: Dict[str, Any]) -> Alert:
        """Create an alert from a rule and metrics."""
        # Extract relevant metrics for message formatting
        format_data = self._extract_format_data(rule.name, metrics)
        
        message = rule.message_template.format(**format_data)
        
        return Alert(
            name=rule.name,
            severity=rule.severity,
            message=message,
            metadata=format_data
        )
    
    def _extract_format_data(self, rule_name: str, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant data for alert message formatting."""
        data = {}
        
        if rule_name == "high_cpu_usage":
            data["cpu_percent"] = metrics.get("system", {}).get("cpu_percent", 0)
        
        elif rule_name == "high_memory_usage":
            data["memory_percent"] = metrics.get("system", {}).get("memory", {}).get("percent", 0)
        
        elif rule_name == "low_disk_space":
            data["disk_percent"] = metrics.get("system", {}).get("disk", {}).get("percent", 0)
        
        elif rule_name == "queue_overload":
            queue_sizes = metrics.get("queues", {}).get("queue_sizes", {})
            data["max_queue_size"] = max(queue_sizes.values()) if queue_sizes else 0
        
        elif rule_name == "database_unhealthy":
            db_health = metrics.get("database", {}).get("health", {})
            data["db_error"] = db_health.get("error", "Unknown error")
        
        elif rule_name == "high_error_rate":
            app_metrics = metrics.get("application", {})
            status_codes = app_metrics.get("status_codes", {})
            total = sum(status_codes.values())
            errors = sum(count for status, count in status_codes.items() if int(status) >= 400)
            data["error_rate"] = (errors / total * 100) if total > 0 else 0
        
        return data
    
    def _resolve_alert(self, alert_name: str):
        """Resolve an active alert."""
        if alert_name in self.active_alerts:
            alert = self.active_alerts[alert_name]
            alert.status = AlertStatus.RESOLVED
            del self.active_alerts[alert_name]
            
            logger.info(f"🟢 Alert resolved: {alert_name}")
    
    def _log_alert(self, alert: Alert):
        """Log an alert with appropriate severity."""
        emoji = {
            AlertSeverity.INFO: "ℹ️",
            AlertSeverity.WARNING: "⚠️",
            AlertSeverity.CRITICAL: "🚨"
        }
        
        log_message = f"{emoji[alert.severity]} ALERT [{alert.severity.value.upper()}]: {alert.message}"
        
        if alert.severity == AlertSeverity.CRITICAL:
            logger.critical(log_message)
        elif alert.severity == AlertSeverity.WARNING:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history."""
        return self.alert_history[-limit:]
    
    def acknowledge_alert(self, alert_name: str) -> bool:
        """Acknowledge an active alert."""
        if alert_name in self.active_alerts:
            self.active_alerts[alert_name].status = AlertStatus.ACKNOWLEDGED
            logger.info(f"Alert acknowledged: {alert_name}")
            return True
        return False
    
    def add_custom_rule(self, rule: AlertRule):
        """Add a custom alert rule."""
        self.rules.append(rule)
        logger.info(f"Added custom alert rule: {rule.name}")
    
    def disable_rule(self, rule_name: str) -> bool:
        """Disable an alert rule."""
        for rule in self.rules:
            if rule.name == rule_name:
                rule.enabled = False
                logger.info(f"Disabled alert rule: {rule_name}")
                return True
        return False
    
    def enable_rule(self, rule_name: str) -> bool:
        """Enable an alert rule."""
        for rule in self.rules:
            if rule.name == rule_name:
                rule.enabled = True
                logger.info(f"Enabled alert rule: {rule_name}")
                return True
        return False


# Global alert manager instance
alert_manager = AlertManager()


async def check_alerts_periodically(interval_seconds: int = 60):
    """Periodically check metrics and generate alerts."""
    while True:
        try:
            # Import here to avoid circular imports
            from api.routers.health import get_system_metrics
            from models.database import db
            
            # Collect metrics
            metrics = {
                "system": get_system_metrics(),
                "database": {
                    "health": db.health_check(),
                    "pool_status": db.get_pool_status(),
                }
            }
            
            # Check for alerts
            alert_manager.check_metrics(metrics)
            
        except Exception as e:
            logger.error(f"Error in alert checking: {e}")
        
        await asyncio.sleep(interval_seconds)


# Export main components
__all__ = [
    'AlertManager',
    'Alert',
    'AlertRule',
    'AlertSeverity',
    'AlertStatus',
    'alert_manager',
    'check_alerts_periodically',
]
