"""
Log aggregation and centralized logging for monitoring and debugging.
"""
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
from datetime import datetime, timezone
from loguru import logger

from core.config import get_settings

settings = get_settings()


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: float
    level: str
    message: str
    module: str
    function: str
    line: int
    extra: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert log entry to dictionary."""
        return {
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp, tz=timezone.utc).isoformat(),
            "level": self.level,
            "message": self.message,
            "module": self.module,
            "function": self.function,
            "line": self.line,
            "extra": self.extra,
        }


class LogAggregator:
    """
    Simple log aggregator for collecting and analyzing application logs.
    """
    
    def __init__(self, max_entries: int = 10000):
        self.max_entries = max_entries
        self.log_entries: deque = deque(maxlen=max_entries)
        self.log_stats: Dict[str, int] = defaultdict(int)
        self.error_patterns: Dict[str, int] = defaultdict(int)
        self.performance_metrics: Dict[str, List[float]] = defaultdict(list)
        
    def add_log_entry(self, record: Dict[str, Any]):
        """Add a log entry to the aggregator."""
        try:
            # Extract information from loguru record
            entry = LogEntry(
                timestamp=record.get("time", time.time()),
                level=record.get("level", {}).get("name", "INFO"),
                message=record.get("message", ""),
                module=record.get("name", "unknown"),
                function=record.get("function", "unknown"),
                line=record.get("line", 0),
                extra=record.get("extra", {})
            )
            
            # Add to collection
            self.log_entries.append(entry)
            
            # Update statistics
            self._update_stats(entry)
            
        except Exception as e:
            # Avoid infinite recursion by not using logger here
            print(f"Error adding log entry: {e}")
    
    def _update_stats(self, entry: LogEntry):
        """Update log statistics."""
        # Count by level
        self.log_stats[f"level_{entry.level.lower()}"] += 1
        self.log_stats["total"] += 1
        
        # Track error patterns
        if entry.level in ["ERROR", "CRITICAL"]:
            # Extract error pattern (first 100 chars of message)
            pattern = entry.message[:100]
            self.error_patterns[pattern] += 1
        
        # Track performance metrics from structured logs
        if "duration" in entry.extra:
            try:
                duration = float(entry.extra["duration"])
                self.performance_metrics[entry.function].append(duration)
                
                # Keep only last 1000 measurements per function
                if len(self.performance_metrics[entry.function]) > 1000:
                    self.performance_metrics[entry.function] = \
                        self.performance_metrics[entry.function][-1000:]
            except (ValueError, TypeError):
                pass
    
    def get_recent_logs(self, 
                       limit: int = 100, 
                       level_filter: Optional[str] = None,
                       module_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get recent log entries with optional filtering.
        
        Args:
            limit: Maximum number of entries to return
            level_filter: Filter by log level (e.g., "ERROR")
            module_filter: Filter by module name
            
        Returns:
            List of log entry dictionaries
        """
        filtered_logs = []
        
        # Iterate through logs in reverse order (most recent first)
        for entry in reversed(self.log_entries):
            # Apply filters
            if level_filter and entry.level != level_filter.upper():
                continue
            
            if module_filter and module_filter not in entry.module:
                continue
            
            filtered_logs.append(entry.to_dict())
            
            # Stop when we have enough entries
            if len(filtered_logs) >= limit:
                break
        
        return filtered_logs
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """Get log statistics and metrics."""
        current_time = time.time()
        one_hour_ago = current_time - 3600
        one_day_ago = current_time - 86400
        
        # Count recent logs
        recent_hour = sum(1 for entry in self.log_entries if entry.timestamp > one_hour_ago)
        recent_day = sum(1 for entry in self.log_entries if entry.timestamp > one_day_ago)
        
        # Count errors in recent periods
        errors_hour = sum(
            1 for entry in self.log_entries 
            if entry.timestamp > one_hour_ago and entry.level in ["ERROR", "CRITICAL"]
        )
        errors_day = sum(
            1 for entry in self.log_entries 
            if entry.timestamp > one_day_ago and entry.level in ["ERROR", "CRITICAL"]
        )
        
        # Calculate error rates
        error_rate_hour = (errors_hour / recent_hour * 100) if recent_hour > 0 else 0
        error_rate_day = (errors_day / recent_day * 100) if recent_day > 0 else 0
        
        return {
            "total_entries": len(self.log_entries),
            "max_entries": self.max_entries,
            "level_counts": dict(self.log_stats),
            "recent_activity": {
                "last_hour": recent_hour,
                "last_day": recent_day,
            },
            "error_metrics": {
                "errors_last_hour": errors_hour,
                "errors_last_day": errors_day,
                "error_rate_hour_percent": round(error_rate_hour, 2),
                "error_rate_day_percent": round(error_rate_day, 2),
            },
            "top_error_patterns": dict(
                sorted(self.error_patterns.items(), key=lambda x: x[1], reverse=True)[:10]
            ),
            "performance_summary": self._get_performance_summary(),
        }
    
    def _get_performance_summary(self) -> Dict[str, Dict[str, float]]:
        """Get performance metrics summary."""
        summary = {}
        
        for function, durations in self.performance_metrics.items():
            if durations:
                summary[function] = {
                    "count": len(durations),
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "p95_duration": self._percentile(durations, 95),
                }
        
        return summary
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a list of numbers."""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def get_error_analysis(self) -> Dict[str, Any]:
        """Get detailed error analysis."""
        current_time = time.time()
        one_hour_ago = current_time - 3600
        
        # Get recent errors
        recent_errors = [
            entry for entry in self.log_entries
            if entry.timestamp > one_hour_ago and entry.level in ["ERROR", "CRITICAL"]
        ]
        
        # Group errors by module
        errors_by_module = defaultdict(int)
        errors_by_function = defaultdict(int)
        
        for entry in recent_errors:
            errors_by_module[entry.module] += 1
            errors_by_function[f"{entry.module}.{entry.function}"] += 1
        
        return {
            "recent_errors_count": len(recent_errors),
            "errors_by_module": dict(errors_by_module),
            "errors_by_function": dict(
                sorted(errors_by_function.items(), key=lambda x: x[1], reverse=True)[:10]
            ),
            "recent_error_samples": [
                entry.to_dict() for entry in recent_errors[-5:]  # Last 5 errors
            ],
        }
    
    def search_logs(self, 
                   query: str, 
                   limit: int = 50,
                   case_sensitive: bool = False) -> List[Dict[str, Any]]:
        """
        Search logs by message content.
        
        Args:
            query: Search query string
            limit: Maximum number of results
            case_sensitive: Whether search should be case sensitive
            
        Returns:
            List of matching log entries
        """
        results = []
        search_query = query if case_sensitive else query.lower()
        
        for entry in reversed(self.log_entries):
            message = entry.message if case_sensitive else entry.message.lower()
            
            if search_query in message:
                results.append(entry.to_dict())
                
                if len(results) >= limit:
                    break
        
        return results
    
    def clear_old_logs(self, older_than_hours: int = 24):
        """Clear logs older than specified hours."""
        cutoff_time = time.time() - (older_than_hours * 3600)
        
        # Count logs to be removed
        old_count = sum(1 for entry in self.log_entries if entry.timestamp < cutoff_time)
        
        # Filter out old logs
        self.log_entries = deque(
            (entry for entry in self.log_entries if entry.timestamp >= cutoff_time),
            maxlen=self.max_entries
        )
        
        logger.info(f"Cleared {old_count} log entries older than {older_than_hours} hours")
        
        return old_count
    
    def export_logs(self, 
                   format: str = "json",
                   limit: Optional[int] = None) -> str:
        """
        Export logs in specified format.
        
        Args:
            format: Export format ("json" or "csv")
            limit: Maximum number of entries to export
            
        Returns:
            Exported logs as string
        """
        entries = list(self.log_entries)
        if limit:
            entries = entries[-limit:]
        
        if format.lower() == "json":
            return json.dumps([entry.to_dict() for entry in entries], indent=2)
        
        elif format.lower() == "csv":
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow(["timestamp", "datetime", "level", "module", "function", "line", "message"])
            
            # Write data
            for entry in entries:
                entry_dict = entry.to_dict()
                writer.writerow([
                    entry_dict["timestamp"],
                    entry_dict["datetime"],
                    entry_dict["level"],
                    entry_dict["module"],
                    entry_dict["function"],
                    entry_dict["line"],
                    entry_dict["message"]
                ])
            
            return output.getvalue()
        
        else:
            raise ValueError(f"Unsupported export format: {format}")


# Global log aggregator instance
log_aggregator = LogAggregator()


def setup_log_aggregation():
    """Setup log aggregation by adding a custom handler to loguru."""
    def log_sink(record):
        """Custom log sink for aggregation."""
        log_aggregator.add_log_entry(record)
    
    # Add the aggregation sink to loguru
    logger.add(log_sink, level="DEBUG", format="{message}")
    logger.info("✅ Log aggregation setup complete")


# Export main components
__all__ = [
    'LogAggregator',
    'LogEntry',
    'log_aggregator',
    'setup_log_aggregation',
]
