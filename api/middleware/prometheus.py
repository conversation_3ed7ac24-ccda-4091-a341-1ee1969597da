"""
Prometheus metrics middleware and endpoints for monitoring.
"""
import time
from typing import Optional
from fastapi import Request, Response
from fastapi.responses import PlainTextResponse
from prometheus_client import (
    Counter, Histogram, Gauge, Info, generate_latest, 
    CollectorRegistry, CONTENT_TYPE_LATEST
)
from loguru import logger

from core.config import get_settings

settings = get_settings()

# Create a custom registry for our metrics
registry = CollectorRegistry()

# HTTP Request metrics
http_requests_total = Counter(
    'http_requests_total',
    'Total number of HTTP requests',
    ['method', 'endpoint', 'status_code'],
    registry=registry
)

http_request_duration_seconds = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint'],
    registry=registry
)

http_requests_in_progress = Gauge(
    'http_requests_in_progress',
    'Number of HTTP requests currently being processed',
    registry=registry
)

# Application metrics
app_info = Info(
    'app_info',
    'Application information',
    registry=registry
)

# Task queue metrics
task_queue_size = Gauge(
    'task_queue_size',
    'Number of tasks in queue',
    ['queue_type'],
    registry=registry
)

task_queue_active_workers = Gauge(
    'task_queue_active_workers',
    'Number of active workers',
    ['worker_type'],
    registry=registry
)

task_processing_duration_seconds = Histogram(
    'task_processing_duration_seconds',
    'Task processing duration in seconds',
    ['task_type'],
    registry=registry
)

# Database metrics
database_connections_active = Gauge(
    'database_connections_active',
    'Number of active database connections',
    registry=registry
)

database_connections_pool_size = Gauge(
    'database_connections_pool_size',
    'Database connection pool size',
    registry=registry
)

database_query_duration_seconds = Histogram(
    'database_query_duration_seconds',
    'Database query duration in seconds',
    ['operation'],
    registry=registry
)

# System metrics
system_cpu_usage_percent = Gauge(
    'system_cpu_usage_percent',
    'System CPU usage percentage',
    registry=registry
)

system_memory_usage_percent = Gauge(
    'system_memory_usage_percent',
    'System memory usage percentage',
    registry=registry
)

system_disk_usage_percent = Gauge(
    'system_disk_usage_percent',
    'System disk usage percentage',
    registry=registry
)

# Error metrics
application_errors_total = Counter(
    'application_errors_total',
    'Total number of application errors',
    ['error_type', 'endpoint'],
    registry=registry
)

# Rate limiting metrics
rate_limit_hits_total = Counter(
    'rate_limit_hits_total',
    'Total number of rate limit hits',
    ['limit_type', 'user_type'],
    registry=registry
)

# Initialize app info
app_info.info({
    'name': settings.APP_NAME,
    'version': settings.APP_VERSION,
    'environment': getattr(settings, 'ENVIRONMENT', 'development')
})


class PrometheusMiddleware:
    """Middleware to collect Prometheus metrics for HTTP requests."""
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        
    async def __call__(self, request: Request, call_next):
        if not self.enabled:
            return await call_next(request)
            
        # Skip metrics collection for the metrics endpoint itself
        if request.url.path == "/metrics":
            return await call_next(request)
            
        # Extract method and path
        method = request.method
        path = request.url.path
        
        # Normalize path for metrics (remove path parameters)
        normalized_path = self._normalize_path(path)
        
        # Increment in-progress requests
        http_requests_in_progress.inc()
        
        # Start timing
        start_time = time.time()
        
        try:
            # Process request
            response = await call_next(request)
            
            # Record metrics
            duration = time.time() - start_time
            status_code = str(response.status_code)
            
            # Record request metrics
            http_requests_total.labels(
                method=method,
                endpoint=normalized_path,
                status_code=status_code
            ).inc()
            
            http_request_duration_seconds.labels(
                method=method,
                endpoint=normalized_path
            ).observe(duration)
            
            # Record errors if status code indicates error
            if response.status_code >= 400:
                error_type = "client_error" if response.status_code < 500 else "server_error"
                application_errors_total.labels(
                    error_type=error_type,
                    endpoint=normalized_path
                ).inc()
            
            return response
            
        except Exception as e:
            # Record server error
            application_errors_total.labels(
                error_type="server_error",
                endpoint=normalized_path
            ).inc()
            
            # Re-raise the exception
            raise
            
        finally:
            # Decrement in-progress requests
            http_requests_in_progress.dec()
    
    def _normalize_path(self, path: str) -> str:
        """Normalize path for metrics to avoid high cardinality."""
        # Replace common path parameters with placeholders
        normalized = path
        
        # Replace UUIDs and video IDs with placeholders
        import re
        normalized = re.sub(r'/[a-fA-F0-9-]{36}', '/{uuid}', normalized)
        normalized = re.sub(r'/[a-zA-Z0-9_-]{11}', '/{video_id}', normalized)
        normalized = re.sub(r'/\d+', '/{id}', normalized)
        
        return normalized


def update_system_metrics():
    """Update system metrics from psutil."""
    try:
        import psutil
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=0.1)
        system_cpu_usage_percent.set(cpu_percent)
        
        # Memory usage
        memory = psutil.virtual_memory()
        system_memory_usage_percent.set(memory.percent)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        system_disk_usage_percent.set(disk.percent)
        
    except Exception as e:
        logger.warning(f"Failed to update system metrics: {e}")


def update_queue_metrics(task_queue):
    """Update task queue metrics."""
    try:
        # Queue sizes
        queue_sizes = task_queue.get_queue_sizes()
        for queue_type, size in queue_sizes.items():
            task_queue_size.labels(queue_type=queue_type).set(size)
        
        # Active workers
        active_tasks = len(task_queue.active_async_tasks)
        task_queue_active_workers.labels(worker_type="total").set(active_tasks)
        
    except Exception as e:
        logger.warning(f"Failed to update queue metrics: {e}")


def update_database_metrics(db):
    """Update database metrics."""
    try:
        pool_status = db.get_pool_status()
        
        if pool_status.get("status") != "error":
            # Connection pool metrics
            if "checked_out" in pool_status:
                database_connections_active.set(pool_status["checked_out"])
            
            if "size" in pool_status:
                database_connections_pool_size.set(pool_status["size"])
                
    except Exception as e:
        logger.warning(f"Failed to update database metrics: {e}")


async def metrics_endpoint() -> Response:
    """Prometheus metrics endpoint."""
    try:
        # Update metrics before generating output
        from models.database import db
        
        update_system_metrics()
        update_database_metrics(db)
        
        # Generate metrics in Prometheus format
        metrics_data = generate_latest(registry)
        
        return PlainTextResponse(
            content=metrics_data.decode('utf-8'),
            media_type=CONTENT_TYPE_LATEST
        )
        
    except Exception as e:
        logger.error(f"Error generating Prometheus metrics: {e}")
        return PlainTextResponse(
            content="# Error generating metrics\n",
            status_code=500
        )


# Export metrics objects for use in other modules
__all__ = [
    'PrometheusMiddleware',
    'metrics_endpoint',
    'update_system_metrics',
    'update_queue_metrics', 
    'update_database_metrics',
    'http_requests_total',
    'http_request_duration_seconds',
    'task_queue_size',
    'task_processing_duration_seconds',
    'database_query_duration_seconds',
    'application_errors_total',
    'rate_limit_hits_total',
]
