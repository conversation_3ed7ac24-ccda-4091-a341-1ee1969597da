import os
import hashlib
import asyncio
from loguru import logger
from google import genai
from google.genai import types
from dotenv import load_dotenv
from models.database import Database

load_dotenv()
TARGET_API_KEY = os.getenv("GEM_API", "")


class TextSummarizer:
    def __init__(self):
        if not TARGET_API_KEY:
            raise ValueError("GEM_API environment variable is not set")

        self.client = genai.Client(api_key=TARGET_API_KEY)
        self.db = Database()
        logger.info("TextSummarizer initialized with Gemini model and database")

    @staticmethod
    def get_text_hash(text: str) -> str:
        """Generate MD5 hash of the input text"""
        return hashlib.md5(text.strip().encode("utf-8")).hexdigest()

    async def summarize(self, text: str, mode: str = "default") -> str:
        """Summarize the given text using Gemini API or get from cache"""
        try:
            # Generate hash and check cache
            text_hash = self.get_text_hash(text)
            logger.debug(f"Text hash: {text_hash}")

            # Check if we have this summary in database
            original, cached_summary = await self.db.get_summary(text_hash, mode)
            if cached_summary:
                logger.info(
                    f"Found cached summary for hash {text_hash} with mode {mode}"
                )
                return cached_summary

            # If not in cache, generate new summary
            logger.debug(
                f"Starting summarization of text (length: {len(text)}) in {mode} mode"
            )

            from .config_loader import get_model_config

            model_config = get_model_config(mode)
            logger.debug(
                f"Using model configuration: model_name={model_config['model_name']}, prompt={'None' if model_config['prompt'] is None else 'present'}, config={model_config['config']}"
            )

            # Подготовка контента в зависимости от конфигурации
            if model_config["prompt"] is None:
                # Используем system_instruction из конфигурации
                contents = types.Part.from_text(text=text)
            else:
                # Добавляем инструкцию как дополнительную секцию к тексту
                contents = types.Part.from_text(
                    text=f"""
----------------------

{text}

----------------------

# Задание:
{model_config["prompt"]}
"""
                )

            # Выполняем генерацию контента в отдельном потоке через run_in_executor
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model=model_config["model_name"],
                    contents=contents,
                    config=model_config["config"],
                ),
            )

            # Детальное логирование ответа для диагностики
            logger.debug(f"API response received. Response object: {type(response)}")
            # logger.debug(f"Response text: {response.text}")
            logger.debug(
                f"Response candidates: {len(response.candidates) if hasattr(response, 'candidates') and response.candidates else 0}"
            )

            # Проверяем, что ответ содержит текст
            if response.text is None:
                # Логируем детальную информацию об ошибке
                error_details = []
                if hasattr(response, "candidates") and response.candidates:
                    for i, candidate in enumerate(response.candidates):
                        if hasattr(candidate, "finish_reason"):
                            error_details.append(
                                f"Candidate {i}: finish_reason={candidate.finish_reason}"
                            )
                        if hasattr(candidate, "safety_ratings"):
                            error_details.append(
                                f"Candidate {i}: safety_ratings={candidate.safety_ratings}"
                            )

                error_info = (
                    "; ".join(error_details)
                    if error_details
                    else "No detailed error information available"
                )
                logger.error(f"API returned empty response. Details: {error_info}")

                # Определяем причину и создаем понятное сообщение об ошибке
                if "SAFETY" in error_info.upper():
                    raise RuntimeError(
                        "Content was blocked by safety filters. Please try with different text or mode."
                    )
                elif (
                    "MAX_TOKENS" in error_info.upper() or "LENGTH" in error_info.upper()
                ):
                    raise RuntimeError(
                        "Content is too long for processing. Please try with shorter text."
                    )
                else:
                    raise RuntimeError(
                        f"AI model failed to generate response. Details: {error_info}"
                    )

            summary = response.text.strip()

            # Проверяем, что summary не пустой
            if not summary:
                logger.error("API returned empty text content")
                raise RuntimeError(
                    "AI model returned empty response. Please try again or use different mode."
                )

            # Save to database
            await self.db.save_summary(text_hash, text.strip(), summary, mode)

            logger.debug(f"Summarization completed, result length: {len(summary)}")
            return summary

        except RuntimeError as e:
            # Пропускаем наши собственные RuntimeError с понятными сообщениями
            logger.error(f"Summarization error: {str(e)}")
            raise
        except Exception as e:
            error_message = str(e)
            logger.error(
                f"Unexpected summarization error: {error_message}", exc_info=True
            )

            # Создаем специальные исключения для разных типов ошибок
            if "502 Bad Gateway" in error_message or "Server Error" in error_message:
                raise RuntimeError(
                    "AI service is temporarily unavailable. Please try again later."
                ) from e
            elif "401" in error_message or "Unauthorized" in error_message:
                raise RuntimeError(
                    "AI service authentication failed. Please contact support."
                ) from e
            elif "429" in error_message or "quota" in error_message.lower():
                raise RuntimeError(
                    "AI service quota exceeded. Please try again later."
                ) from e
            elif "timeout" in error_message.lower():
                raise RuntimeError(
                    "AI service request timed out. Please try again with shorter text."
                ) from e
            else:
                raise RuntimeError(
                    "Summarization failed due to unexpected error. Please try again or contact support."
                ) from e
