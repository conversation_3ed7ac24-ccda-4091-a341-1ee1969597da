-- PostgreSQL Debug Queries for Cache Investigation
-- Run these queries to analyze the cache behavior

-- 1. Check total number of records in subtitles table
SELECT 'Total records in subtitles table:' as description, COUNT(*) as count FROM subtitles;

-- 2. Show all video IDs and their basic info
SELECT 
    'All video IDs in database:' as description,
    video_id, 
    title,
    CASE 
        WHEN en_subtitles IS NOT NULL AND LENGTH(en_subtitles) > 0 THEN 'YES'
        ELSE 'NO'
    END as has_en_subs,
    CASE 
        WHEN ru_subtitles IS NOT NULL AND LENGTH(ru_subtitles) > 0 THEN 'YES'
        ELSE 'NO'
    END as has_ru_subs,
    LENGTH(COALESCE(en_subtitles, '')) as en_length,
    LENGTH(COALESCE(ru_subtitles, '')) as ru_length
FROM subtitles 
ORDER BY video_id;

-- 3. Check for specific video ID that should exist (replace with actual ID)
-- Example: EbEdL7hHJng or SAa6xFyATcw
SELECT 
    'Specific video check:' as description,
    video_id,
    title,
    original_language,
    publish_date,
    CASE 
        WHEN en_subtitles IS NOT NULL AND LENGTH(en_subtitles) > 0 THEN 'YES'
        ELSE 'NO'
    END as has_en_subs,
    CASE 
        WHEN ru_subtitles IS NOT NULL AND LENGTH(ru_subtitles) > 0 THEN 'YES'
        ELSE 'NO'
    END as has_ru_subs,
    LENGTH(COALESCE(en_subtitles, '')) as en_length,
    LENGTH(COALESCE(ru_subtitles, '')) as ru_length
FROM subtitles 
WHERE video_id = 'SAa6xFyATcw';  -- Replace with the video ID you're testing

-- 4. Check for any NULL or empty subtitle content
SELECT 
    'Records with NULL or empty subtitles:' as description,
    video_id,
    title,
    CASE 
        WHEN en_subtitles IS NULL THEN 'NULL'
        WHEN LENGTH(en_subtitles) = 0 THEN 'EMPTY'
        ELSE 'HAS_CONTENT'
    END as en_status,
    CASE 
        WHEN ru_subtitles IS NULL THEN 'NULL'
        WHEN LENGTH(ru_subtitles) = 0 THEN 'EMPTY'
        ELSE 'HAS_CONTENT'
    END as ru_status
FROM subtitles
WHERE (en_subtitles IS NULL OR LENGTH(en_subtitles) = 0) 
   AND (ru_subtitles IS NULL OR LENGTH(ru_subtitles) = 0);

-- 5. Show database connection info
SELECT 
    'Database connection info:' as description,
    current_database() as database_name,
    current_user as user_name,
    inet_server_addr() as server_address,
    inet_server_port() as server_port;

-- 6. Check table schema
SELECT 
    'Table schema:' as description,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'subtitles' 
ORDER BY ordinal_position;

-- 7. Check for any constraints or indexes
SELECT 
    'Table constraints:' as description,
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'subtitles';

-- 8. Show recent activity (if you have timestamps)
-- This might not work if there are no timestamp columns
SELECT 
    'Sample of recent records:' as description,
    video_id,
    title,
    original_language
FROM subtitles 
ORDER BY video_id DESC 
LIMIT 5;

-- 9. Check for duplicate video IDs (should be none due to primary key)
SELECT 
    'Duplicate video IDs check:' as description,
    video_id,
    COUNT(*) as count
FROM subtitles 
GROUP BY video_id 
HAVING COUNT(*) > 1;

-- 10. Check character encoding issues
SELECT 
    'Character encoding check:' as description,
    video_id,
    title,
    LENGTH(video_id) as video_id_length,
    ASCII(SUBSTRING(video_id, 1, 1)) as first_char_ascii
FROM subtitles 
WHERE video_id IN ('EbEdL7hHJng', 'PF2wLKv2lsI')  -- Replace with your test IDs
ORDER BY video_id;
