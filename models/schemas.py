import re
from enum import Enum
from typing import Optional, List
from pydantic import BaseModel, HttpUrl, Field, validator

# Динамическое создание SummarizeMode на основе доступных режимов в MODEL_CONFIGS
from worker.summarizers.config_loader import get_config_loader

# Import custom validators
from .validators import (
    validate_youtube_url,
    validate_client_uid,
    validate_text_content,
    validate_language_code,
    validate_filename,
    youtube_url_field,
    client_uid_field,
    text_content_field,
    ValidationConstants,
)


class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class FileType(str, Enum):
    TXT = "text/plain"
    MD = "text/markdown"


class MessageType(str, Enum):
    STATUS = "status_update"
    RESULT = "result"
    TEXT = "text_input"
    FILE = "file_input"


class SubtitleRequest(BaseModel):
    """Request model for subtitle extraction with enhanced validation."""

    url: str = youtube_url_field()
    client_uid: Optional[str] = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only

    @validator("url")
    def validate_url(cls, v):
        """Validate that the URL is a proper YouTube URL."""
        return validate_youtube_url(v)

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)

    class Config:
        json_schema_extra = {
            "example": {
                "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "client_uid": "user_123",
            }
        }


class SubtitleResponse(BaseModel):
    """Response model for subtitle extraction with enhanced validation."""

    status: TaskStatus
    task_id: str = Field(
        ..., min_length=1, max_length=100, description="Unique task identifier"
    )
    client_uid: Optional[str] = client_uid_field()
    title: Optional[str] = Field(None, max_length=500, description="Video title")
    original_language: Optional[str] = Field(
        None, description="ISO 639-1 language code"
    )
    publish_date: Optional[str] = Field(None, description="Video publish date")
    en_subtitles: Optional[str] = Field(None, description="English subtitles")
    ru_subtitles: Optional[str] = Field(None, description="Russian subtitles")
    error: Optional[str] = Field(
        None, max_length=1000, description="Error message if task failed"
    )

    @validator("original_language")
    def validate_language(cls, v):
        """Validate language code format."""
        return validate_language_code(v)

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class VideoListRequest(BaseModel):
    """Request model for video list extraction with enhanced validation."""

    url: str = youtube_url_field()
    client_uid: Optional[str] = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only

    @validator("url")
    def validate_url(cls, v):
        """Validate that the URL is a proper YouTube URL."""
        return validate_youtube_url(v)

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class VideoListResponse(BaseModel):
    """Response model for video list extraction with enhanced validation."""

    status: TaskStatus
    task_id: str = Field(
        ..., min_length=1, max_length=100, description="Unique task identifier"
    )
    client_uid: Optional[str] = client_uid_field()
    video_ids: Optional[List[str]] = Field(
        None, description="List of extracted video IDs"
    )
    error: Optional[str] = Field(
        None, max_length=1000, description="Error message if task failed"
    )

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints."""
        return validate_client_uid(v)

    @validator("video_ids")
    def validate_video_ids(cls, v):
        """Validate video IDs format."""
        if v is None:
            return v

        if not isinstance(v, list):
            raise ValueError("video_ids must be a list")

        if len(v) > 1000:  # Reasonable limit
            raise ValueError("Too many video IDs (max 1000)")

        # Validate each video ID format
        for video_id in v:
            if not isinstance(video_id, str):
                raise ValueError("All video IDs must be strings")
            if not re.match(r"^[a-zA-Z0-9_-]{11}$", video_id):
                raise ValueError(f"Invalid video ID format: {video_id}")

        return v


class SummarizeMode(str, Enum):
    # Автоматически создаем элементы Enum из ключей MODEL_CONFIGS
    # Используем dict comprehension для создания атрибутов
    locals().update({key.upper(): key for key in get_config_loader().available_modes})


class SummarizeRequest(BaseModel):
    """Request model for text summarization with enhanced validation."""

    client_uid: Optional[str] = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only
    mode: Optional[SummarizeMode] = Field(None, description="Summarization mode")
    og_text: str = text_content_field(
        min_length=10,
        max_length=500_000,  # 500KB of text
        description="Original text to summarize",
    )

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)

    @validator("og_text")
    def validate_text(cls, v):
        """Validate text content."""
        return validate_text_content(v, min_length=10, max_length=500_000)

    class Config:
        json_schema_extra = {
            "example": {
                "client_uid": "user_123",
                "mode": "default",
                "og_text": "This is a sample text that needs to be summarized. It should be long enough to demonstrate the summarization capabilities of the system.",
            }
        }


class SummarizeResponse(BaseModel):
    """Response model for text summarization with enhanced validation."""

    status: TaskStatus
    task_id: str = Field(
        ..., min_length=1, max_length=100, description="Unique task identifier"
    )
    client_uid: Optional[str] = client_uid_field()
    summary: Optional[str] = Field(None, description="Generated summary")
    error: Optional[str] = Field(
        None, max_length=1000, description="Error message if task failed"
    )

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class WebSocketMessage(BaseModel):
    """Base WebSocket message with enhanced validation."""

    type: MessageType
    status: Optional[TaskStatus] = None
    task_id: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Task identifier"
    )
    client_uid: Optional[str] = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only
    error: Optional[str] = Field(None, max_length=1000, description="Error message")

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)


class WebSocketSubtitleMessage(WebSocketMessage):
    """Subtitle-specific WebSocket message with enhanced validation."""

    title: Optional[str] = Field(None, max_length=500, description="Video title")
    original_language: Optional[str] = Field(
        None, description="ISO 639-1 language code"
    )
    publish_date: Optional[str] = Field(None, description="Video publish date")
    en_subtitles: Optional[str] = Field(None, description="English subtitles")
    ru_subtitles: Optional[str] = Field(None, description="Russian subtitles")

    @validator("original_language")
    def validate_language(cls, v):
        """Validate language code format."""
        return validate_language_code(v)


class WebSocketSummarizeMessage(WebSocketMessage):
    """Summarize-specific WebSocket message with enhanced validation."""

    summary: Optional[str] = Field(None, description="Generated summary")


class ConvertTTMLRequest(BaseModel):
    """Request model for TTML to TXT conversion with enhanced validation."""

    ttml_text: str = Field(
        ...,
        min_length=10,
        max_length=10_000_000,  # 10MB of text
        description="TTML content to convert to TXT format",
    )

    @validator("ttml_text")
    def validate_ttml_content(cls, v):
        """Validate TTML content format."""
        if not v.strip():
            raise ValueError("TTML content cannot be empty")

        # Basic TTML format validation - should contain XML-like structure
        if not ("<tt" in v.lower() or "<p>" in v.lower() or "<?xml" in v.lower()):
            raise ValueError("Content does not appear to be valid TTML format")

        return v

    class Config:
        json_schema_extra = {
            "example": {
                "ttml_text": '<?xml version="1.0" encoding="utf-8"?><tt><body><div><p>Hello world</p></div></body></tt>'
            }
        }


class ConvertTTMLResponse(BaseModel):
    """Response model for TTML to TXT conversion with enhanced validation."""

    status: TaskStatus
    txt_content: Optional[str] = Field(None, description="Converted TXT content")
    error: Optional[str] = Field(
        None, max_length=1000, description="Error message if conversion failed"
    )

    class Config:
        json_schema_extra = {
            "example": {"status": "completed", "txt_content": "Hello world"}
        }


class WebSocketSummarizeRequest(BaseModel):
    """Request for summarization via WebSocket with enhanced validation."""

    type: MessageType = Field(
        ..., description="Message type (text_input or file_input)"
    )
    client_uid: Optional[str] = (
        client_uid_field()
    )  # Deprecated: kept for backward compatibility only
    mode: Optional[SummarizeMode] = Field(None, description="Summarization mode")
    content: str = Field(
        ...,
        min_length=1,
        max_length=10_000_000,
        description="Text content or base64 encoded file content",
    )
    filename: Optional[str] = Field(
        None, description="Filename (required for file_input)"
    )

    @validator("client_uid")
    def validate_client_uid_format(cls, v):
        """Validate client UID format and constraints. Note: client_uid is deprecated and ignored in processing."""
        return validate_client_uid(v)

    @validator("filename")
    def validate_filename_format(cls, v):
        """Validate filename format and security."""
        return validate_filename(v)

    @validator("content")
    def validate_content_by_type(cls, v, values):
        """Validate content based on message type."""
        message_type = values.get("type")

        if message_type == MessageType.TEXT:
            # For text input, validate as regular text
            return validate_text_content(v, min_length=10, max_length=500_000)
        elif message_type == MessageType.FILE:
            # For file input, content should be base64 encoded
            if not v:
                raise ValueError("File content cannot be empty")

            # Basic base64 validation (simplified)
            import base64

            try:
                # Try to decode to validate base64 format
                decoded = base64.b64decode(v, validate=True)
                if len(decoded) > ValidationConstants.MAX_FILE_SIZE:
                    raise ValueError(f"File size exceeds maximum allowed size")
            except Exception:
                raise ValueError("Invalid base64 encoded file content")

        return v

    @validator("filename")
    def validate_filename_required_for_file(cls, v, values):
        """Ensure filename is provided for file input."""
        message_type = values.get("type")

        if message_type == MessageType.FILE and not v:
            raise ValueError("Filename is required for file input")

        return v
