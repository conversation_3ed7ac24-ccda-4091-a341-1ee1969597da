from sqlalchemy import create_engine, Column, String, Text
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool, StaticPool
from loguru import logger
import os
from dotenv import load_dotenv
from contextlib import contextmanager
from typing import Generator

load_dotenv()

DATABASE_TYPE = os.getenv(
    "DATABASE_TYPE", "sqlite").lower()  # postgres or sqlite
POSTGRES_HOST = os.getenv("POSTGRES_HOST", "localhost")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "")
POSTGRES_DB = os.getenv("POSTGRES_DB", "yt_subs")

# Connection pool settings
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "10"))
DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "20"))
DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))
DB_POOL_RECYCLE = int(os.getenv("DB_POOL_RECYCLE", "3600"))  # 1 hour

# Create base class for declarative models
Base = declarative_base()


class Subtitles(Base):
    """Model for storing YouTube video subtitles"""

    __tablename__ = "subtitles"

    video_id = Column(String(20), primary_key=True)
    title = Column(String(500))  # YouTube video titles can be quite long
    original_language = Column(String(10))  # ISO 639-1 language code
    publish_date = Column(String(20))  # Дата публикации видео
    en_subtitles = Column(Text)
    ru_subtitles = Column(Text)


class Summary(Base):
    """Model for storing text summarization results"""

    __tablename__ = "summaries"

    text_hash = Column(String(32), primary_key=True)  # MD5 hash
    mode = Column(String(20), primary_key=True)  # Режим суммаризации
    original_text = Column(Text)
    summary_text = Column(Text)


class Database:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            try:
                self.initialize()
                self._initialized = True
            except Exception as e:
                logger.error(f"❌ Database initialization failed: {e}")
                # Don't re-raise the exception to allow the application to start
                # The database will be marked as unhealthy in health checks
                self._initialized = False

    def initialize(self):
        """Initialize database connection with connection pooling based on configuration"""
        if DATABASE_TYPE == "postgres":
            # Get database URL from secrets manager
            from core.secrets import secrets_manager

            try:
                db_url = secrets_manager.get_database_url()
                logger.info("✅ Database URL obtained from secrets manager")
            except ValueError as e:
                logger.error(f"❌ Database configuration error: {e}")
                raise

            # Configure connection pool for PostgreSQL
            self.engine = create_engine(
                db_url,
                echo=False,
                poolclass=QueuePool,
                pool_size=DB_POOL_SIZE,
                max_overflow=DB_MAX_OVERFLOW,
                pool_timeout=DB_POOL_TIMEOUT,
                pool_recycle=DB_POOL_RECYCLE,
                pool_pre_ping=True,  # Validate connections before use
            )

            logger.info(
                f"PostgreSQL connection pool configured: "
                f"pool_size={DB_POOL_SIZE}, max_overflow={DB_MAX_OVERFLOW}, "
                f"timeout={DB_POOL_TIMEOUT}s, recycle={DB_POOL_RECYCLE}s"
            )
        else:
            # Initialize SQLite database with StaticPool for thread safety
            self.engine = create_engine(
                "sqlite:///data.db",
                echo=False,
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 20
                },
                pool_pre_ping=True
            )

            logger.info("SQLite database configured with StaticPool for thread safety")

        self.SessionLocal = sessionmaker(bind=self.engine)

        # Create tables
        try:
            Base.metadata.create_all(self.engine)
            logger.info(
                f"✅ {DATABASE_TYPE.capitalize()} database tables created successfully"
            )
        except SQLAlchemyError as e:
            logger.error(
                f"❌ Error creating {DATABASE_TYPE} database tables: {e}")

            # For PostgreSQL, provide helpful error messages
            if DATABASE_TYPE == "postgres":
                if "could not connect to server" in str(e).lower():
                    logger.error(
                        "💡 PostgreSQL connection failed. Please check:\n"
                        "   - PostgreSQL server is running\n"
                        "   - Connection parameters in .env are correct\n"
                        "   - Network connectivity to database server"
                    )
                elif "authentication failed" in str(e).lower():
                    logger.error(
                        "💡 PostgreSQL authentication failed. Please check:\n"
                        "   - POSTGRES_PASSWORD is correct\n"
                        "   - POSTGRES_USER has proper permissions\n"
                        "   - Database POSTGRES_DB exists"
                    )
                elif "database" in str(e).lower() and "does not exist" in str(e).lower():
                    logger.error(
                        "💡 PostgreSQL database does not exist. Please:\n"
                        "   - Create the database specified in POSTGRES_DB\n"
                        "   - Or update POSTGRES_DB to an existing database"
                    )

            raise

    def get_session(self):
        """Get a new database session"""
        if not self._initialized:
            raise RuntimeError("Database not initialized. Check database configuration and connectivity.")
        return self.SessionLocal()

    @contextmanager
    def get_session_context(self) -> Generator:
        """
        Context manager for database sessions with automatic cleanup.

        Usage:
            with db.get_session_context() as session:
                # Use session here
                pass
        """
        if not self._initialized:
            raise RuntimeError("Database not initialized. Check database configuration and connectivity.")

        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def get_pool_status(self) -> dict:
        """Get connection pool status for monitoring"""
        if not self._initialized or not hasattr(self, 'engine') or self.engine is None:
            return {"status": "not_initialized", "type": "unknown"}

        if not hasattr(self.engine, 'pool'):
            return {"status": "no_pool", "type": "unknown"}

        pool = self.engine.pool

        try:
            status = {
                "pool_class": pool.__class__.__name__,
                "size": getattr(pool, 'size', lambda: 0)(),
                "checked_in": getattr(pool, 'checkedin', lambda: 0)(),
                "checked_out": getattr(pool, 'checkedout', lambda: 0)(),
                "overflow": getattr(pool, 'overflow', lambda: 0)(),
                "invalid": getattr(pool, 'invalid', lambda: 0)(),
            }

            # Add PostgreSQL specific metrics
            if DATABASE_TYPE == "postgres":
                status.update({
                    "max_overflow": DB_MAX_OVERFLOW,
                    "pool_timeout": DB_POOL_TIMEOUT,
                    "pool_recycle": DB_POOL_RECYCLE,
                })

            return status
        except Exception as e:
            logger.warning(f"Failed to get pool status: {e}")
            return {"status": "error", "error": str(e)}

    def health_check(self) -> dict:
        """Perform database health check"""
        if not self._initialized:
            return {
                "status": "unhealthy",
                "database_type": DATABASE_TYPE,
                "error": "Database not initialized",
                "connection_test": "failed",
                "initialization_status": "failed"
            }

        try:
            with self.get_session_context() as session:
                # Simple query to test connection
                session.execute("SELECT 1")

            pool_status = self.get_pool_status()

            return {
                "status": "healthy",
                "database_type": DATABASE_TYPE,
                "pool_status": pool_status,
                "connection_test": "passed",
                "initialization_status": "success"
            }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "database_type": DATABASE_TYPE,
                "error": str(e),
                "connection_test": "failed",
                "initialization_status": "success" if self._initialized else "failed"
            }

    async def get_subtitles(
        self, video_id: str
    ) -> tuple[str | None, str | None, str | None, str | None, str | None]:
        """Get subtitles from database if they exist.
        Returns (title, original_language, publish_date, en_subtitles, ru_subtitles)"""
        logger.debug(f"Checking cache for video ID: {video_id}")
        try:
            with self.get_session_context() as session:
                result = session.query(Subtitles).filter_by(
                    video_id=video_id).first()
                if result and (result.en_subtitles or result.ru_subtitles):
                    logger.debug(
                        f"Cache hit for video {video_id}. Found title: {result.title}, "
                        f"original language: {result.original_language}, "
                        f"publish date: {result.publish_date}, "
                        f"en subs length: {len(result.en_subtitles) if result.en_subtitles else 0}, "
                        f"ru subs length: {len(result.ru_subtitles) if result.ru_subtitles else 0}"
                    )
                    return (
                        result.title,
                        result.original_language,
                        result.publish_date,
                        result.en_subtitles,
                        result.ru_subtitles,
                    )
                logger.debug(f"Cache miss for video {video_id}")
                return None, None, None, None, None
        except SQLAlchemyError as e:
            logger.error(f"Database error while fetching subtitles: {e}")
            return None, None, None, None, None

    async def save_subtitles(
        self,
        video_id: str,
        title: str | None,
        original_language: str | None,
        publish_date: str | None,
        en_text: str | None,
        ru_text: str | None,
    ) -> bool:
        """Save subtitles to database"""
        logger.debug(
            f"Saving subtitles to cache for video {video_id}. "
            f"Title: {title}, Original language: {original_language}, "
            f"Publish date: {publish_date}, "
            f"EN subs length: {len(en_text) if en_text else 0}, "
            f"RU subs length: {len(ru_text) if ru_text else 0}"
        )
        try:
            with self.get_session_context() as session:
                subtitle = Subtitles(
                    video_id=video_id,
                    title=title,
                    original_language=original_language,
                    publish_date=publish_date,
                    en_subtitles=en_text,
                    ru_subtitles=ru_text,
                )
                # Use merge instead of add to handle updates
                session.merge(subtitle)
                # Commit is handled by context manager
                logger.debug(
                    f"Successfully saved subtitles to cache for video {video_id}"
                )
                logger.info(
                    f"Saved subtitles for video {video_id} with original language: {original_language} and publish date: {publish_date}"
                )
                return True
        except SQLAlchemyError as e:
            logger.error(f"Database error while saving subtitles: {e}")
            return False

    async def get_summary(
        self, text_hash: str, mode: str = "default"
    ) -> tuple[str | None, str | None]:
        """Get summary from database if it exists"""
        try:
            with self.get_session_context() as session:
                result = (
                    session.query(Summary)
                    .filter_by(text_hash=text_hash, mode=mode)
                    .first()
                )
                if result and result.summary_text:
                    return result.original_text, result.summary_text
                return None, None
        except SQLAlchemyError as e:
            logger.error(f"Database error while fetching summary: {e}")
            return None, None

    async def save_summary(
        self,
        text_hash: str,
        original_text: str,
        summary_text: str,
        mode: str = "default",
    ) -> bool:
        """Save summary to database"""
        try:
            with self.get_session_context() as session:
                summary = Summary(
                    text_hash=text_hash,
                    mode=mode,
                    original_text=original_text,
                    summary_text=summary_text,
                )
                # Use merge instead of add to handle updates
                session.merge(summary)
                # Commit is handled by context manager
                logger.info(
                    f"Saved summary for hash {text_hash} with mode {mode}")
                return True
        except SQLAlchemyError as e:
            logger.error(f"Database error while saving summary: {e}")
            return False


# Create singleton database instance
db = Database()

# Re-export the class and instance
__all__ = ["Database", "db"]
